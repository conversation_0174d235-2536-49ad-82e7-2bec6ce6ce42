import React from 'react';

const AMPVideo = ({ 
  src, 
  poster, 
  width, 
  height, 
  layout = "responsive",
  autoplay = true,
  loop = true,
  muted = true,
  animateIn,
  animateInDelay,
  animateInDuration,
  className
}) => {
  const videoProps = {
    width: width,
    height: height,
    layout: layout,
    poster: poster,
    autoplay: autoplay,
    loop: loop,
    muted: muted
  };

  if (animateIn) {
    videoProps['animate-in'] = animateIn;
  }

  if (animateInDelay) {
    videoProps['animate-in-delay'] = animateInDelay;
  }

  if (animateInDuration) {
    videoProps['animate-in-duration'] = animateInDuration;
  }

  if (className) {
    videoProps.className = className;
  }

  return (
    <amp-video {...videoProps}>
      <source src={src} type="video/mp4" />
    </amp-video>
  );
};

export default AMPVideo;
