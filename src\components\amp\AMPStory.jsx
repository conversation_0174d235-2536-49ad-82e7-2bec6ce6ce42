import React from 'react';
import Head from 'next/head';

const AMPStory = ({ 
  title, 
  publisher, 
  publisherLogoSrc, 
  posterPortraitSrc, 
  posterSquareSrc, 
  posterLandscapeSrc,
  children,
  canonicalUrl,
  storyUrl
}) => {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={title} />
        <link rel="canonical" href={canonicalUrl || storyUrl} />
        
        {/* AMP Story Metadata */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "http://schema.org",
            "@type": "Article",
            "mainEntityOfPage": {
              "@type": "WebPage",
              "@id": canonicalUrl || storyUrl
            },
            "headline": title,
            "publisher": {
              "@type": "Organization",
              "name": publisher,
              "logo": {
                "@type": "ImageObject",
                "url": publisherLogoSrc
              }
            },
            "datePublished": new Date().toISOString(),
            "dateModified": new Date().toISOString()
          })}
        </script>

        {/* Open Graph Meta Tags */}
        <meta property="og:type" content="article" />
        <meta property="og:title" content={title} />
        <meta property="og:image" content={posterSquareSrc} />
        <meta property="og:url" content={canonicalUrl || storyUrl} />
        
        {/* Twitter Card Meta Tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:image" content={posterSquareSrc} />
      </Head>
      
      <amp-story
        standalone=""
        title={title}
        publisher={publisher}
        publisher-logo-src={publisherLogoSrc}
        poster-portrait-src={posterPortraitSrc}
        poster-square-src={posterSquareSrc}
        poster-landscape-src={posterLandscapeSrc}
      >
        {children}
        
        {/* Bookend */}
        <amp-story-bookend 
          src="/api/webstories/bookend" 
          layout="nodisplay"
        />
      </amp-story>
    </>
  );
};

export default AMPStory;
