import { Html, <PERSON>, <PERSON>, NextScript } from "next/document";

export default function AMPDocument() {
  return (
    <Html lang="en" amp="">
      <Head>
        <meta charSet="utf-8" />
        <script async src="https://cdn.ampproject.org/v0.js"></script>
        <script
          async
          custom-element="amp-story"
          src="https://cdn.ampproject.org/v0/amp-story-1.0.js"
        ></script>
        <script
          async
          custom-element="amp-video"
          src="https://cdn.ampproject.org/v0/amp-video-0.1.js"
        ></script>
        <script
          async
          custom-element="amp-img"
          src="https://cdn.ampproject.org/v0/amp-img-0.1.js"
        ></script>
        <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1" />
        <link rel="canonical" href="self" />
        <style amp-boilerplate="">
          {`body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`}
        </style>
        <noscript>
          <style amp-boilerplate="">
            {`body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`}
          </style>
        </noscript>
        <style amp-custom="">
          {`
            amp-story {
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            }
            amp-story-page * {
              color: white;
              text-align: center;
            }
            .story-title {
              font-size: 2.5rem;
              font-weight: bold;
              text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
              margin: 0;
              padding: 20px;
            }
            .story-text {
              font-size: 1.2rem;
              line-height: 1.4;
              text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
              margin: 0;
              padding: 20px;
            }
            .story-overlay {
              background: linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0.7));
            }
            [template="fill"] {
              display: flex;
              align-items: center;
              justify-content: center;
            }
            [template="vertical"] {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              padding: 20px;
            }
            [template="horizontal"] {
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: space-between;
              padding: 20px;
            }
            [template="thirds"] {
              display: grid;
              grid-template-rows: 1fr 1fr 1fr;
              grid-template-areas: 
                "upper-third"
                "middle-third" 
                "lower-third";
            }
            [grid-area="upper-third"] {
              grid-area: upper-third;
            }
            [grid-area="middle-third"] {
              grid-area: middle-third;
            }
            [grid-area="lower-third"] {
              grid-area: lower-third;
            }
          `}
        </style>
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
