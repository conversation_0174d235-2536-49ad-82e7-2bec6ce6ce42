export default function handler(req, res) {
  const bookendData = {
    "bookendVersion": "v1.0",
    "shareProviders": [
      "facebook",
      "twitter",
      "email",
      "whatsapp"
    ],
    "components": [
      {
        "type": "heading",
        "text": "More Stories"
      },
      {
        "type": "small",
        "title": "Explore More Web Stories",
        "url": "/webstories",
        "image": "/images/webstories-logo.png"
      },
      {
        "type": "landscape",
        "title": "Latest Articles",
        "url": "/",
        "image": "/images/latest-articles.png",
        "category": "News"
      }
    ]
  };

  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.status(200).json(bookendData);
}
