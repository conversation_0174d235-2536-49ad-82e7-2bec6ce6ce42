import React from 'react';

const AMPImage = ({ 
  src, 
  alt, 
  width, 
  height, 
  layout = "responsive",
  animateIn,
  animateInDelay,
  animateInDuration,
  objectFit = "cover",
  className
}) => {
  const imgProps = {
    src: src,
    alt: alt || "",
    width: width,
    height: height,
    layout: layout,
    'object-fit': objectFit
  };

  if (animateIn) {
    imgProps['animate-in'] = animateIn;
  }

  if (animateInDelay) {
    imgProps['animate-in-delay'] = animateInDelay;
  }

  if (animateInDuration) {
    imgProps['animate-in-duration'] = animateInDuration;
  }

  if (className) {
    imgProps.className = className;
  }

  return <amp-img {...imgProps} />;
};

export default AMPImage;
