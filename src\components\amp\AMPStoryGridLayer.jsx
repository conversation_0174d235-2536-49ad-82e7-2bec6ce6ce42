import React from 'react';

const AMPStoryGridLayer = ({ 
  template = "fill", 
  children,
  animateIn,
  animateInDelay,
  animateInDuration,
  gridArea
}) => {
  const layerProps = {
    template: template
  };

  if (animateIn) {
    layerProps['animate-in'] = animateIn;
  }

  if (animateInDelay) {
    layerProps['animate-in-delay'] = animateInDelay;
  }

  if (animateInDuration) {
    layerProps['animate-in-duration'] = animateInDuration;
  }

  if (gridArea) {
    layerProps['grid-area'] = gridArea;
  }

  return (
    <amp-story-grid-layer {...layerProps}>
      {children}
    </amp-story-grid-layer>
  );
};

export default AMPStoryGridLayer;
