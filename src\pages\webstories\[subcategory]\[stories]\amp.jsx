import React from "react";
import { getWebStories } from "@/pages/api/WebStoriesApi";
import { convertToISTISOString } from "@/utils/Util";
import { Const } from "@/utils/Constants";
import { 
  AMPStory, 
  AMPStoryPage, 
  AMPStoryGridLayer, 
  AMPImage 
} from "@/components/amp";

export const config = { amp: true };

const AMPWebStoryDetail = ({ data, canonicalUrl }) => {
  if (!data || !data.slides || data.slides.length === 0) {
    return <div>Story not found</div>;
  }

  const firstSlide = data.slides[0];
  const storyTitle = firstSlide?.title || "Web Story";
  const publisherLogo = `${Const.ClientLink}/images/logo.png`;
  const posterImage = firstSlide?.image || "";

  return (
    <AMPStory
      title={storyTitle}
      publisher="Manifest Magazine"
      publisherLogoSrc={publisherLogo}
      posterPortraitSrc={posterImage}
      posterSquareSrc={posterImage}
      posterLandscapeSrc={posterImage}
      canonicalUrl={canonicalUrl}
      storyUrl={canonicalUrl}
    >
      {data.slides.map((slide, index) => (
        <AMPStoryPage
          key={`story-page-${index}`}
          id={`page-${index + 1}`}
          autoAdvanceAfter="5s"
        >
          {/* Background Image Layer */}
          <AMPStoryGridLayer template="fill">
            <AMPImage
              src={slide.image}
              alt={slide.altName || slide.title}
              width="720"
              height="1280"
              layout="responsive"
              objectFit="cover"
            />
          </AMPStoryGridLayer>

          {/* Overlay for better text readability */}
          <AMPStoryGridLayer template="fill">
            <div className="story-overlay"></div>
          </AMPStoryGridLayer>

          {/* Text Content Layer */}
          <AMPStoryGridLayer 
            template="vertical"
            animateIn="fade-in"
            animateInDelay="0.3s"
          >
            <div className="story-content-wrapper">
              <h1 className="story-title">{slide.title}</h1>
              {slide.description && (
                <div 
                  className="story-text"
                  dangerouslySetInnerHTML={{ __html: slide.description }}
                />
              )}
              
              {/* Credits and metadata for first slide */}
              {index === 0 && (
                <div className="story-metadata">
                  {slide.contributor && slide.contributor.length > 0 && (
                    <p className="story-credit">
                      Photo Credit: {slide.contributor.join(", ")}
                    </p>
                  )}
                  {slide.timestamp && (
                    <p className="story-date">
                      Published: {new Date(slide.timestamp).toLocaleDateString()}
                    </p>
                  )}
                </div>
              )}
            </div>
          </AMPStoryGridLayer>
        </AMPStoryPage>
      ))}
    </AMPStory>
  );
};

export async function getServerSideProps(context) {
  const { subcategory, stories } = context.params;
  
  try {
    const response = await getWebStories(subcategory, stories);
    
    if (!response || !response.data) {
      return {
        notFound: true,
      };
    }

    const canonicalUrl = `${Const.ClientLink}/webstories/${subcategory}/${stories}`;

    return {
      props: {
        data: response.data,
        canonicalUrl,
      },
    };
  } catch (error) {
    console.error("Error fetching web story:", error);
    return {
      notFound: true,
    };
  }
}

export default AMPWebStoryDetail;
