#!/usr/bin/env node

/**
 * AMP Validation Script for Web Stories
 * 
 * This script validates AMP stories to ensure they comply with Google's AMP specifications.
 * It checks both individual story pages and provides detailed validation reports.
 */

const amphtmlValidator = require('amphtml-validator');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
const OUTPUT_DIR = './validation-reports';

// Sample story URLs to validate (replace with actual story paths)
const STORY_URLS = [
  '/webstories/entertainment/sample-story/amp',
  '/webstories/lifestyle/another-story/amp',
  // Add more story URLs as needed
];

/**
 * Initialize AMP validator
 */
async function initValidator() {
  try {
    const validator = await amphtmlValidator.getInstance();
    console.log('✅ AMP Validator initialized successfully');
    return validator;
  } catch (error) {
    console.error('❌ Failed to initialize AMP validator:', error);
    process.exit(1);
  }
}

/**
 * Fetch HTML content from URL
 */
async function fetchHTML(url) {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return await response.text();
  } catch (error) {
    console.error(`❌ Failed to fetch ${url}:`, error.message);
    return null;
  }
}

/**
 * Validate single AMP document
 */
function validateAMP(validator, html, url) {
  const result = validator.validateString(html);
  
  const report = {
    url,
    isValid: result.status === 'PASS',
    status: result.status,
    errors: [],
    warnings: []
  };

  // Process validation results
  for (const error of result.errors) {
    const issue = {
      line: error.line,
      col: error.col,
      message: error.message,
      code: error.code,
      severity: error.severity
    };

    if (error.severity === 'ERROR') {
      report.errors.push(issue);
    } else if (error.severity === 'WARNING') {
      report.warnings.push(issue);
    }
  }

  return report;
}

/**
 * Generate validation report
 */
function generateReport(reports) {
  const timestamp = new Date().toISOString();
  const summary = {
    timestamp,
    totalStories: reports.length,
    validStories: reports.filter(r => r.isValid).length,
    invalidStories: reports.filter(r => !r.isValid).length,
    totalErrors: reports.reduce((sum, r) => sum + r.errors.length, 0),
    totalWarnings: reports.reduce((sum, r) => sum + r.warnings.length, 0)
  };

  const report = {
    summary,
    details: reports
  };

  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  // Write detailed report
  const reportPath = path.join(OUTPUT_DIR, `amp-validation-${Date.now()}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  return { report, reportPath };
}

/**
 * Print console summary
 */
function printSummary(summary, reportPath) {
  console.log('\n' + '='.repeat(60));
  console.log('🔍 AMP VALIDATION SUMMARY');
  console.log('='.repeat(60));
  console.log(`📊 Total Stories: ${summary.totalStories}`);
  console.log(`✅ Valid Stories: ${summary.validStories}`);
  console.log(`❌ Invalid Stories: ${summary.invalidStories}`);
  console.log(`🚨 Total Errors: ${summary.totalErrors}`);
  console.log(`⚠️  Total Warnings: ${summary.totalWarnings}`);
  console.log(`📄 Detailed Report: ${reportPath}`);
  console.log('='.repeat(60));

  if (summary.invalidStories > 0) {
    console.log('\n❌ Some stories failed validation. Check the detailed report for issues.');
    process.exit(1);
  } else {
    console.log('\n🎉 All stories passed AMP validation!');
  }
}

/**
 * Main validation function
 */
async function validateStories() {
  console.log('🚀 Starting AMP Story validation...\n');

  const validator = await initValidator();
  const reports = [];

  for (const storyPath of STORY_URLS) {
    const url = `${BASE_URL}${storyPath}`;
    console.log(`🔍 Validating: ${url}`);

    const html = await fetchHTML(url);
    if (!html) {
      reports.push({
        url,
        isValid: false,
        status: 'FETCH_ERROR',
        errors: [{ message: 'Failed to fetch HTML content' }],
        warnings: []
      });
      continue;
    }

    const report = validateAMP(validator, html, url);
    reports.push(report);

    // Print immediate feedback
    if (report.isValid) {
      console.log(`  ✅ Valid (${report.warnings.length} warnings)`);
    } else {
      console.log(`  ❌ Invalid (${report.errors.length} errors, ${report.warnings.length} warnings)`);
    }
  }

  const { report, reportPath } = generateReport(reports);
  printSummary(report.summary, reportPath);
}

/**
 * CLI interface
 */
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
AMP Story Validation Tool

Usage:
  node scripts/validate-amp.js [options]

Options:
  --help, -h     Show this help message
  --url <url>    Validate a specific story URL
  --local        Use localhost:3000 as base URL (default)
  --prod         Use production URL for validation

Examples:
  node scripts/validate-amp.js
  node scripts/validate-amp.js --url /webstories/tech/ai-story/amp
  node scripts/validate-amp.js --prod
    `);
    process.exit(0);
  }

  // Handle single URL validation
  const urlIndex = args.indexOf('--url');
  if (urlIndex !== -1 && args[urlIndex + 1]) {
    STORY_URLS.length = 0; // Clear default URLs
    STORY_URLS.push(args[urlIndex + 1]);
  }

  // Handle production URL
  if (args.includes('--prod')) {
    BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://manifestmagazine.in';
  }

  validateStories().catch(error => {
    console.error('💥 Validation failed:', error);
    process.exit(1);
  });
}

module.exports = { validateStories, validateAMP, initValidator };
