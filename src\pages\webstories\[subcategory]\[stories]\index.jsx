import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import Head from "next/head";
import gsap from "gsap";
import SplitText from "gsap/dist/SplitText";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import { getWebStories } from "@/pages/api/WebStoriesApi";
import { convertToISTISOString, dateFormateWithTimeShort, htmlParser } from "@/utils/Util";
import { BsArrowUpRight } from "react-icons/bs";
import { BsArrowUpLeft } from "react-icons/bs";
import { RxCross1 } from "react-icons/rx";
import ImageGallerySchema from "@/components/seo/ImageGallerySchema";
import MediaGallerySchema from "@/components/seo/MediaGallerySchema";
import { usePathname } from "next/navigation";
import { Const } from "@/utils/Constants";
import { FaArrowLeftLong, FaArrowRightLong } from "react-icons/fa6";
import { useGSAP } from "@gsap/react";
import { SlArrowLeft, SlArrowRight } from "react-icons/sl";
import { useRouter } from "next/router";

gsap.registerPlugin(SplitText);
const WebStoryDetail = ({ meta, data, previousData, nextData, breadcrumbs, tag }) => {
	const pathname = usePathname();
	const [direction, setDirection] = useState(-1);
	const [covered_slide, set_covered_slide] = useState(0);
	const router = useRouter();

	var scale = 10;
	var directions = true;
	var x = 300; // Example value, replace with actual value

	var t = scale;
	var e = directions ? -7 : 7;
	var o = directions ? -0.1 : 0.1;
	var n = directions ? -100 : 100;
	var l = directions ? -x / 3 : x / 3;

	// let cursor = (e) => {
	//   const reel_cursor = document.querySelector(".reel-cursor");
	//   gsap.to(reel_cursor, {
	//     top: e.clientY,
	//     left: e.clientX,
	//   });
	//   if (e.clientX < window.innerWidth / 2) {
	//     direction !== 0 && setDirection(0);
	//   } else {
	//     direction !== 1 && setDirection(1);
	//   }
	// };

	useEffect(() => {
		const webslidebtn = document.querySelectorAll(
			".main_div,.prevCntrArrow,.nextCntrArrow, .story_content_row.bottom a"
		);
		webslidebtn.forEach((btn, i) => {
			btn.addEventListener("mouseenter", () => {
				gsap.to(".reel-cursor", {
					scale: 0,
					duration: 0.4,
					ease: "power2.in",
				});
				gsap.to(btn.querySelectorAll("h4"), {
					transform: "translateY(0%)",
					delay: 0.3,
					duration: 0.3,
					stagger: {
						amount: 0.1,
					},
				});
			});
			btn.addEventListener("mouseleave", () => {
				gsap.to(".reel-cursor", {
					scale: 1,
					duration: 0.4,
				});
				gsap.to(btn.querySelectorAll("h4"), {
					transform: "translateY(100%)",
					//  delay: 0.2,
					duration: 0.3,
					stagger: {
						amount: 0.1,
					},
				});
			});
		});
		document.querySelector(".webstorieclose").addEventListener("mouseenter", () => {
			gsap.to(".reel-cursor", {
				scale: 0,
				duration: 0.3,
				ease: "power2.inOut",
			});
		});
		document.querySelector(".webstorieclose").addEventListener("mouseleave", () => {
			gsap.to(".reel-cursor", {
				scale: 1,
				duration: 0.3,
				ease: "power2.inOut",
			});
		});

		// document
		//   .querySelector(".webstorieclose svg")
		//   .addEventListener("click", () => {
		//     window.location.href = "/";
		//   });
	}, []);

	useGSAP(() => {
		const slidesArray = document.querySelectorAll(".story_image_contentCntr");
		const barArray = document.querySelectorAll(".bar-loader");

		let curr_slide = 0;
		let barTween; // Store GSAP tween for bar animation

		// Update animateBar to not auto-advance
		const animateBar = (index) => {
			// Clear previous bar animation
			if (barTween) barTween.kill();

			// Reset all bars
			barArray.forEach((bar, i) => {
				gsap.set(bar, { width: i <= index - 1 ? "100%" : "0%" });
			});

			// Set current bar to 100% without auto-advancing
			barTween = gsap.to(barArray[index], {
				width: "100%",
				ease: "linear",
				duration: 0.5, // Quick animation to show progress
			});
		};

		// Slide animation with direction (1 = next, -1 = prev)
		const slideAnimation = (direction = 1) => {
			const next_slide = (curr_slide + direction + slidesArray.length) % slidesArray.length;
			const currentSlide = slidesArray[curr_slide];
			const nextSlide = slidesArray[next_slide];

			gsap.set(currentSlide, { zIndex: 2 });

			const fromClip =
				direction === 1
					? "polygon(125% 0%, 100% 0%, 100% 100%, 100% 100%)"
					: "polygon(-25% 0%, 0% 0%, 0% 100%, -25% 100%)";

			gsap.set(nextSlide, {
				zIndex: 3,
				clipPath: fromClip,
				scale: 1.8,
			});

			const tlh = gsap.timeline();
			tlh.to(nextSlide, {
				duration: 1,
				clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
				scale: 1,
				ease: "power2.inOut",
				onComplete: () => {
					gsap.set(currentSlide, { zIndex: 1 });
					curr_slide = next_slide;
					animateBar(curr_slide); // Update bar for new slide
				},
			});
		};

		// Initialize first slide
		animateBar(curr_slide);

		const nextBtn = document.querySelector(".nextCntrArrow");
		const prevBtn = document.querySelector(".prevCntrArrow");

		if (nextBtn) {
			nextBtn.addEventListener("click", () => {
				slideAnimation(1);
			});
		}

		if (prevBtn) {
			prevBtn.addEventListener("click", () => {
				slideAnimation(-1);
			});
		}

		// Detect click on left/right half of screen to trigger prev/next slide
		window.addEventListener("click", (e) => {
			const middle = window.innerWidth / 2;
			const clickX = e.clientX;

			if (clickX < middle) {
				// Clicked on left half — go to previous slide
				slideAnimation(-1);
			} else {
				// Clicked on right half — go to next slide
				slideAnimation(1);
			}
		});

		// Cleanup
		return () => {
			if (barTween) barTween.kill();
			if (nextBtn) nextBtn.removeEventListener("click", slideAnimation);
			if (prevBtn) prevBtn.removeEventListener("click", slideAnimation);
		};
	}, []);

	return (
		<>
			<Head>
				<link
					rel="amphtml"
					href={`${Const.ClientLink}/webstories/${router.query.subcategory}/${router.query.stories}/amp`}
				/>
			</Head>
			<SeoHeader meta={meta} />
			<BreadcrumbSchema itemList={breadcrumbs} />
			<ImageGallerySchema
				title={data?.slides?.[0]?.title || ""}
				description={data?.slides?.[0]?.description || ""}
				url={Const.ClientLink + pathname}
				datePublished={data?.timestamp || ""}
				data={data?.slides || []}
			/>
			<MediaGallerySchema
				title={data?.slides?.[0]?.title || ""}
				description={data?.slides?.[0]?.description || ""}
				data={data?.slides || []}
			/>
			<Link href={"/"} className="webstorieclose">
				<RxCross1 />
			</Link>
			<div className="webstorieReels">
				<div className="arrowsDir prevNextBtnWrapper">
					<div className="prevCntrArrow">
						<SlArrowLeft />
					</div>
					<div className="arrowsDir nextCntrArrow">
						<SlArrowRight />
					</div>
				</div>
				<div className="story_content">
					<div className="indices">
						{/* {data.slides.map((inde, index) => {
              return (
                <div
                  key={`indices_item_${index}`}
                  className="index"
                >
                  <div
                    className="index-highlight"
                    style={{
                      width: covered_slide >= index ? "100%" : "0%",
                    }}
                  ></div>
                </div>
              );
            })} */}
						{Array.from({ length: data.slides.length }).map((_, i) => (
							<div key={i} className="home-slide-bar">
								<div className="bar-loader"></div>
							</div>
						))}
					</div>
					{data.slides.map((slide, index) => {
						return (
							<div
								className="story_image_contentCntr"
								key={index}
								style={
									index !== 0
										? {
												clipPath: "polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)",
										  }
										: {
												clipPath: "polygon(100% 0%, 0% 0%, 0% 100%, 100% 100%)",
										  }
								}
							>
								{/* Image and Text Container */}
								<div className="story_reelstext_cntr">
									{/* Text content for each story */}
									<div className="story_content_row bottom">
										<div className="story_content_title">
											<div className="story_content_title-cntr">
												<h1>{slide?.title ?? ""}</h1>
												{htmlParser(slide?.description ?? "")}
											</div>
										</div>
										<div className="story_content_row_credit">
											{/* <Link href="" alt target="_blank"> */}
											{slide.contributor.length > 0 && (
												<small>Photo Credit: {slide.contributor.join(", ")}</small>
											)}
											{index === 0 ? (
												<small>Publish Date: {dateFormateWithTimeShort(slide?.timestamp)}</small>
											) : null}
											{/* </Link> */}
										</div>
									</div>
								</div>

								<div key={index} className="story_imgs">
									<Image
										src={slide?.image ?? ""}
										alt={slide?.altName ?? ""}
										fill
										sizes="(max-width: 1024px) 1280px, (max-width: 1280px) 1920px, 2400px)"
									/>
								</div>
							</div>
						);
					})}
				</div>
				<div className="webstorieReels_bottom">
					{previousData?.slug && (
						<Link href={previousData?.slug ?? "#"} className="main_div">
							<div className="text-div">
								<div className="inner-text-div prev_divtext">{previousData?.title ?? ""}</div>
							</div>
							<div className="content-div">
								<div className="img-div">
									<img src={previousData?.coverImg ?? ""} alt={previousData?.altName ?? ""} />
								</div>

								<div className="btn-div prevbtn_div"> Previous story</div>
							</div>
						</Link>
					)}
					{nextData?.slug && (
						<Link href={nextData?.slug ?? "#"} className="main_div">
							<div className="text-div next_divtextcntr">
								<div className="inner-text-div next_divtext">{nextData?.title ?? ""}</div>
							</div>
							<div className="content-div">
								<div className="btn-div nextbtn_div"> Next story</div>
								<div className="img-div">
									<img src={nextData?.coverImg ?? ""} alt={nextData?.altName ?? ""} />
								</div>
							</div>
						</Link>
					)}
				</div>
			</div>
		</>
	);
};

export default WebStoryDetail;

export async function getServerSideProps(context) {
	const { stories } = context.params;
	const url = `/${stories}`;
	try {
		const storiesRes = await getWebStories(url);

		// const authorRes = await getStoriesAuthor(url);
		if (!storiesRes || Object.keys(storiesRes.data).length === 0) {
			return {
				notFound: true,
			};
		}

		const storyData = storiesRes.data.current.data;
		const newObject = {
			title: storyData.title,
			description: "",
			image: storyData.coverImg,
			altName: storyData.altName,
			sequence: -1,
			contributor: [],
			timestamp: storyData.timestamp,
		};

		// Prepend new object to slides array
		if (Array.isArray(storyData.slides)) {
			storyData.slides.unshift(newObject);
		}

		return {
			props: {
				data: storyData ?? {},
				previousData: storiesRes.data.previous ?? {},
				nextData: storiesRes.data.next ?? {},
				breadcrumbs: storiesRes.data.current.breadcrumbs ?? [],
				tag: storiesRes.data.current.tag ?? [],
				meta: storiesRes.data.current.meta ?? {},
			},
		};
	} catch (error) {
		console.error("Error fetching data:", error.message);
		return {
			notFound: true,
		};
	}
}
