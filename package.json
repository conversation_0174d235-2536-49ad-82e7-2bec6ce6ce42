{"name": "manifest_project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "validate-amp": "node scripts/validate-amp.js", "validate-amp:prod": "node scripts/validate-amp.js --prod"}, "dependencies": {"@ampproject/toolbox-optimizer": "^2.10.1", "@gsap/react": "^2.1.1", "@studio-freight/react-lenis": "^0.0.47", "@tiptap/react": "^2.10.3", "amphtml-validator": "^1.0.38", "gsap": "npm:@gsap/business@^3.12.5", "html-react-parser": "^5.2.0", "locomotive-scroll": "^5.0.0-beta.21", "next": "14.2.6", "react": "^18", "react-datepicker": "^7.5.0", "react-dom": "^18", "react-icons": "^5.3.0", "react-outside-click-handler": "^1.3.0", "react-player": "^2.16.0", "react-tweet": "^3.2.1", "scrollmagic": "^2.0.8", "swiper": "^11.1.12"}}