import { Html, <PERSON>, <PERSON>, NextScript } from "next/document";
import <PERSON>ript from "next/script";
import { useRouter } from "next/router";

export default function Document() {
	return (
		<Html lang="en">
			<Head>
				<link rel="stylesheet" href="https://use.typekit.net/ben8rqm.css" />
				<link
					rel="preload"
					href="https://securepubads.g.doubleclick.net/tag/js/gpt.js"
					as="script"
				/>
				{/* Google Tag Manager (gtag.js) */}
				<script async src="https://www.googletagmanager.com/gtag/js?id=G-YJC4Z5844L"></script>
				<script
					dangerouslySetInnerHTML={{
						__html: `
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'G-YJC4Z5844L');
          `,
					}}
				/>
				{/* Meta Pixel Code */}
				<script
					dangerouslySetInnerHTML={{
						__html: `
              !function(f,b,e,v,n,t,s) {
                if(f.fbq)return;
                n=f.fbq=function(){n.callMethod?
                n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                if(!f._fbq)f._fbq=n;
                n.push=n;
                n.loaded=!0;
                n.version='2.0';
                n.queue=[];
                t=b.createElement(e);
                t.async=!0;
                t.src=v;
                s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)
              }(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');

              fbq('init', '957856323160808');
              fbq('track', 'PageView');
            `,
					}}
				/>
				<noscript>
					<img
						height="1"
						width="1"
						style={{ display: "none" }}
						src="https://www.facebook.com/tr?id=957856323160808&ev=PageView&noscript=1"
					/>
				</noscript>
			</Head>
			<body>
				<Main />
				<Script
					async
					src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"
					strategy="beforeInteractive"
				/>
				<NextScript />
			</body>
		</Html>
	);
}
