{"summary": {"timestamp": "2025-07-07T10:12:51.376Z", "totalStories": 2, "validStories": 0, "invalidStories": 2, "totalErrors": 2, "totalWarnings": 0}, "details": [{"url": "http://localhost:3000/webstories/entertainment/sample-story/amp", "isValid": false, "status": "FETCH_ERROR", "errors": [{"message": "Failed to fetch HTML content"}], "warnings": []}, {"url": "http://localhost:3000/webstories/lifestyle/another-story/amp", "isValid": false, "status": "FETCH_ERROR", "errors": [{"message": "Failed to fetch HTML content"}], "warnings": []}]}