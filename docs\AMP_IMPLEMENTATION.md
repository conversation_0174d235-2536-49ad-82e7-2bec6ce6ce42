# AMP Web Stories Implementation Guide

## Overview

This document outlines the implementation of AMP (Accelerated Mobile Pages) compliance for Manifest Magazine's web stories. The implementation ensures that web stories meet Google's AMP specifications for improved performance, SEO, and discoverability.

## Architecture

### Dual Implementation Strategy

The project uses a **hybrid approach** with both regular and AMP versions of web stories:

- **Regular Stories**: `/webstories/[subcategory]/[stories]/` - Full-featured with GSAP animations
- **AMP Stories**: `/webstories/[subcategory]/[stories]/amp` - AMP-compliant lightweight version

### Key Components

#### 1. AMP Story Components (`src/components/amp/`)

- **AMPStory.jsx**: Main wrapper with metadata and SEO
- **AMPStoryPage.jsx**: Individual story pages with auto-advance
- **AMPStoryGridLayer.jsx**: Layout component with animation support
- **AMPImage.jsx**: AMP-compliant image component
- **AMPVideo.jsx**: AMP-compliant video component

#### 2. AMP Document Template (`src/pages/_document-amp.jsx`)

- AMP-specific HTML structure with required boilerplate
- AMP runtime scripts and extensions
- Optimized CSS for story layouts

#### 3. Story Pages (`src/pages/webstories/[subcategory]/[stories]/amp.jsx`)

- Server-side rendered AMP story pages
- Dynamic data fetching with `getServerSideProps`
- Proper canonical URL setup

## Features

### ✅ Implemented Features

1. **AMP Compliance**
   - Valid AMP HTML structure
   - Required AMP boilerplate and meta tags
   - AMP runtime and extension scripts

2. **Story Structure**
   - amp-story wrapper with metadata
   - amp-story-page for individual slides
   - amp-story-grid-layer for layouts

3. **Media Support**
   - Responsive images with amp-img
   - Video support with amp-video
   - Proper aspect ratios and object-fit

4. **SEO Optimization**
   - Structured data (JSON-LD)
   - Open Graph and Twitter Card meta tags
   - Canonical URLs and amphtml links

5. **Navigation**
   - Story bookend with sharing options
   - Related content suggestions
   - Auto-advance functionality

6. **Validation**
   - AMP validation script
   - Automated testing tools
   - Error reporting

### 🔄 Configuration

#### Next.js Configuration (`next.config.mjs`)

```javascript
// AMP Configuration
amp: {
  canonicalBase: process.env.NEXT_PUBLIC_SITE_URL || 'https://manifestmagazine.in',
},

// AMP Web Stories rewrites
async rewrites() {
  return [
    {
      source: "/webstories/:subcategory/:stories/amp",
      destination: "/webstories/:subcategory/:stories/amp",
    },
    // ... other rewrites
  ];
}
```

#### Package.json Scripts

```json
{
  "scripts": {
    "validate-amp": "node scripts/validate-amp.js",
    "validate-amp:prod": "node scripts/validate-amp.js --prod"
  }
}
```

## Usage

### Development

1. **Start Development Server**
   ```bash
   npm run dev
   ```

2. **Access AMP Stories**
   ```
   http://localhost:3000/webstories/[subcategory]/[stories]/amp
   ```

3. **Validate AMP Stories**
   ```bash
   npm run validate-amp
   ```

### Production

1. **Build and Deploy**
   ```bash
   npm run build
   npm start
   ```

2. **Validate Production Stories**
   ```bash
   npm run validate-amp:prod
   ```

## File Structure

```
src/
├── components/amp/
│   ├── AMPStory.jsx
│   ├── AMPStoryPage.jsx
│   ├── AMPStoryGridLayer.jsx
│   ├── AMPImage.jsx
│   └── AMPVideo.jsx
├── pages/
│   ├── _document-amp.jsx
│   ├── api/webstories/bookend.js
│   └── webstories/[subcategory]/[stories]/
│       ├── index.jsx (regular version)
│       └── amp.jsx (AMP version)
scripts/
├── validate-amp.js
docs/
└── AMP_IMPLEMENTATION.md
```

## Testing

### Manual Testing

1. **AMP Validator**: Use Google's AMP validator at https://validator.ampproject.org/
2. **Web Stories Testing Tool**: https://search.google.com/test/amp
3. **Rich Results Test**: https://search.google.com/test/rich-results

### Automated Testing

```bash
# Validate all configured stories
npm run validate-amp

# Validate specific story
node scripts/validate-amp.js --url /webstories/tech/ai-story/amp

# Validate production stories
npm run validate-amp:prod
```

## SEO Benefits

1. **Improved Performance**: Faster loading times with AMP optimization
2. **Better Discoverability**: Enhanced visibility in Google Search
3. **Rich Results**: Eligible for story carousels and rich snippets
4. **Mobile Optimization**: Optimized for mobile-first indexing

## Troubleshooting

### Common Issues

1. **AMP Validation Errors**
   - Check required AMP boilerplate
   - Ensure all custom CSS is in AMP style tags
   - Verify amp-story structure

2. **Images Not Loading**
   - Ensure amp-img has layout="responsive"
   - Check image URLs and dimensions
   - Verify CORS headers for external images

3. **Story Not Auto-Advancing**
   - Check auto-advance-after attribute
   - Ensure proper story page structure
   - Verify media loading

### Debug Tools

- Use browser dev tools AMP tab
- Check console for AMP validation warnings
- Use AMP validator bookmarklet

## Performance Metrics

Expected improvements with AMP implementation:

- **Page Load Time**: 50-70% faster
- **First Contentful Paint**: 40-60% improvement  
- **Largest Contentful Paint**: 30-50% improvement
- **Cumulative Layout Shift**: Minimal with proper sizing

## Next Steps

1. **Content Migration**: Convert existing stories to AMP format
2. **Analytics Integration**: Add AMP-compatible analytics
3. **A/B Testing**: Compare performance metrics
4. **SEO Monitoring**: Track search visibility improvements

## Resources

- [AMP Documentation](https://amp.dev/)
- [AMP Stories Guide](https://amp.dev/documentation/guides-and-tutorials/start/visual_story/)
- [Google Web Stories](https://developers.google.com/search/docs/appearance/web-stories)
- [AMP Validator](https://validator.ampproject.org/)
